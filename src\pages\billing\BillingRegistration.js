import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Button, Row, Col, InputGroup, Alert, Badge } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faSave, faPlus, faTrash, faSearch, faUser, faPhone,
  faExclamationTriangle, faCheckCircle, faTimes, faIdCard, faEnvelope,
  faCalendarAlt, faCreditCard, faFlask, faCalculator, faSpinner,
  faFileInvoiceDollar
} from '@fortawesome/free-solid-svg-icons';
import { patientAPI, billingAPI } from '../../services/api';
import billingService from '../../services/billingAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import '../../styles/BillingRegistrationCompact.css';

// Compact Patient Search Component
const PatientSearch = ({ onPatientSelect, searchTerm, setSearchTerm, searchResults, searching }) => {
  const [showResults, setShowResults] = useState(false);

  return (
    <div className="patient-search-compact">
      <InputGroup size="sm">
        <InputGroup.Text>
          <FontAwesomeIcon icon={faSearch} />
        </InputGroup.Text>
        <Form.Control
          type="text"
          placeholder="Search existing patient (name/phone)..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setShowResults(e.target.value.length > 0);
          }}
          onFocus={() => setShowResults(searchTerm.length > 0)}
        />
        {searchTerm && (
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={() => {
              setSearchTerm('');
              setShowResults(false);
            }}
          >
            <FontAwesomeIcon icon={faTimes} />
          </Button>
        )}
      </InputGroup>
      
      {showResults && searchResults.length > 0 && (
        <div className="search-results-compact">
          {searchResults.slice(0, 5).map((patient) => (
            <div
              key={patient.id}
              className="search-result-item"
              onClick={() => {
                onPatientSelect(patient);
                setShowResults(false);
                setSearchTerm('');
              }}
            >
              <div className="patient-name">{patient.first_name} {patient.last_name}</div>
              <div className="patient-details">{patient.phone} • {patient.date_of_birth}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const BillingRegistration = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { tenantData, currentTenantContext } = useTenant();

  // Core states
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Patient search states
  const [patientSearchTerm, setPatientSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);

  // Enhanced form data state with all required fields
  const [formData, setFormData] = useState({
    // Section 1: Branch & Registration Details
    branch: currentUser?.tenant_id || '',
    date: new Date().toISOString().split('T')[0],
    no: '', // Manual entry number
    sidDate: new Date().toISOString().split('T')[0],
    sidNo: '', // Auto-generated SID
    category: 'Normal',

    // Section 2: Patient Information
    patientCode: '',
    title: 'Mr.',
    patientName: '',
    firstName: '',
    lastName: '',
    dob: '',
    ageYears: '',
    ageMonths: '',
    ageInput: '', // Manual age entry
    ageMode: 'dob', // 'dob' or 'manual'
    sex: 'Male',
    mobile: '',
    email: '',
    referrer: 'Doctor',
    source: '',
    collectionBoy: '',
    motherName: '', // For baby patients

    // Section 3: Test Selection
    tests: [],

    // Section 4: Billing Details
    sampleCollectDateTime: new Date().toISOString().slice(0, 16),
    billAmount: 0,
    otherCharges: 0,
    otherChargesDescription: '',
    discountType: 'percentage', // 'percentage' or 'amount'
    discountPercent: 0,
    discountAmount: 0,
    discountRemarks: '',
    totalAmount: 0,
    amountPaid: 0,
    balanceToBePaid: 0,
    finalReportDate: '',

    // Section 5: Payment Details
    paymentMethod: 'Cash',
    bankName: '',
    referenceNumber: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentAmount: 0,

    // Section 6: Clinical & Remarks
    clinicalRemarks: '',
    generalRemarks: '',
    emergency: false,
    deliveryMode: 'Lab Pickup',

    // Section 7: Study/Research Details
    studyNo: '',
    subPeriod: '',
    subjectPeriod: '',
    subNo: ''
  });

  // Test management
  const [availableTests, setAvailableTests] = useState([]);
  const [testSearchTerm, setTestSearchTerm] = useState('');

  // Enhanced master data options
  const branchOptions = ['MAYILADUTHURAI', 'CHENNAI', 'COIMBATORE', 'MADURAI'];
  const categoryOptions = ['Normal', 'Emergency', 'VIP', 'Corporate'];
  const titleOptions = ['Mr.', 'Mrs.', 'Ms.', 'Dr.', 'Baby', 'Master', 'B/Q (Baby/Queen)'];
  const genderOptions = ['Male', 'Female', 'Others'];
  const referralSources = ['Doctor', 'Hospital', 'Corporate', 'Lab', 'Insurance', 'Self/Patient'];
  const referralOptions = {
    'Doctor': ['Dr. Rajesh Kumar', 'Dr. Priya Sharma', 'Dr. Arun Patel', 'Dr. Meera Singh', 'Dr. Suresh Reddy'],
    'Hospital': ['Apollo Hospital', 'Fortis Healthcare', 'Max Hospital', 'AIIMS', 'Government Hospital'],
    'Corporate': ['TCS Health', 'Infosys Wellness', 'Wipro Care', 'HCL Health', 'Tech Mahindra'],
    'Lab': ['SRL Diagnostics', 'Dr. Lal PathLabs', 'Metropolis', 'Thyrocare', 'Quest Diagnostics'],
    'Insurance': ['Star Health', 'HDFC ERGO', 'ICICI Lombard', 'New India Assurance', 'United India'],
    'Self/Patient': ['Walk-in Patient', 'Online Booking', 'Phone Booking', 'Repeat Customer']
  };
  const paymentMethods = ['Cash', 'Card', 'UPI', 'Bank Transfer', 'Cheque'];
  const deliveryModes = ['Home Delivery', 'Lab Pickup', 'Email', 'WhatsApp'];
  const collectionBoys = ['Ravi Kumar', 'Suresh M', 'Prakash S', 'Venkat R'];
  const bankOptions = ['SBI', 'HDFC', 'ICICI', 'Axis Bank', 'Canara Bank', 'Indian Bank'];
  const discountReasons = [
    'Senior Citizen Discount',
    'Employee Discount',
    'Corporate Package',
    'Bulk Test Discount',
    'Loyalty Customer',
    'Medical Emergency',
    'Insurance Adjustment',
    'Promotional Offer',
    'Repeat Customer',
    'Special Circumstances'
  ];

  // Load test profiles on mount
  useEffect(() => {
    loadTestProfiles();
    generateSID();
  }, []);

  const loadTestProfiles = async () => {
    try {
      // Try to fetch test profiles from master data API
      try {
        // First try to get test profiles from admin master data
        const response = await fetch('/api/admin/master-data', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const masterData = await response.json();
          if (masterData.success && masterData.data?.testProfiles) {
            const testProfiles = masterData.data.testProfiles.map(test => ({
              id: test.id,
              name: test.test_profile || test.testName || test.name,
              price: test.test_price || test.price || 0,
              department: test.department || 'General'
            }));
            setAvailableTests(testProfiles);
            return;
          }
        }
      } catch (apiError) {
        console.warn('Master data API not available, trying billing API:', apiError);
      }

      // Fallback to billing API for test data
      try {
        const response = await billingAPI.getAllBillings();
        if (response.success && response.data) {
          // If billing data contains test information, extract it
          const tests = response.data.flatMap(bill => bill.items || [])
            .filter((test, index, self) =>
              index === self.findIndex(t => t.test_id === test.test_id)
            )
            .map(test => ({
              id: test.test_id || test.id,
              name: test.test_name || test.testName || test.name,
              price: test.amount || test.price || 0
            }));

          if (tests.length > 0) {
            setAvailableTests(tests);
            return;
          }
        }
      } catch (apiError) {
        console.warn('Billing API not available, using mock data:', apiError);
      }

      // Mock test data as final fallback
      setAvailableTests([
        { id: 1, name: 'Complete Blood Count (CBC)', price: 300 },
        { id: 2, name: 'Lipid Profile', price: 450 },
        { id: 3, name: 'Liver Function Test', price: 500 },
        { id: 4, name: 'Kidney Function Test', price: 400 },
        { id: 5, name: 'Thyroid Profile', price: 600 },
        { id: 6, name: 'Blood Sugar (Fasting)', price: 150 },
        { id: 7, name: 'HbA1c', price: 350 },
        { id: 8, name: 'Vitamin D', price: 800 },
        { id: 9, name: 'Vitamin B12', price: 700 },
        { id: 10, name: 'ESR', price: 100 }
      ]);
    } catch (error) {
      console.error('Error loading test profiles:', error);
      setError('Failed to load test profiles. Please refresh the page.');
    }
  };

  // Generate SID number
  const generateSID = () => {
    const timestamp = Date.now().toString().slice(-6);
    setFormData(prev => ({
      ...prev,
      sidNo: `SID${timestamp}`
    }));
  };

  // Age calculation functions
  const calculateAgeFromDOB = (dob) => {
    if (!dob) return { years: '', months: '' };

    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();

    if (months < 0) {
      years--;
      months += 12;
    }

    return { years: years.toString(), months: months.toString() };
  };

  // Auto-calculate age when DOB changes
  useEffect(() => {
    if (formData.dob && formData.ageMode === 'dob') {
      const { years, months } = calculateAgeFromDOB(formData.dob);
      setFormData(prev => ({
        ...prev,
        ageYears: years,
        ageMonths: months,
        ageInput: years > 0 ? `${years} years` : `${months} months`
      }));
    }
  }, [formData.dob, formData.ageMode]);

  // Check if patient is baby (< 2 years) to show mother name field
  const isBabyPatient = () => {
    const age = parseInt(formData.ageYears) || 0;
    return age < 2 || formData.title === 'Baby' || formData.title === 'B/Q (Baby/Queen)';
  };

  // Patient search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (patientSearchTerm.trim().length >= 2) {
        handlePatientSearch();
      } else {
        setSearchResults([]);
      }
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [patientSearchTerm]);

  const handlePatientSearch = async () => {
    if (!patientSearchTerm.trim()) return;

    setSearching(true);
    try {
      const response = await patientAPI.searchPatients(patientSearchTerm);
      // Backend returns paginated data with structure {items: [...], page: 1, total_items: X}
      setSearchResults(response.data?.items || []);
    } catch (error) {
      console.error('Error searching patients:', error);
      setSearchResults([]);
    } finally {
      setSearching(false);
    }
  };

  // Handle patient selection from search
  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    const { years, months } = calculateAgeFromDOB(patient.date_of_birth);

    setFormData(prev => ({
      ...prev,
      patientCode: patient.id || '',
      title: patient.title || 'Mr.',
      patientName: `${patient.first_name || ''} ${patient.last_name || ''}`.trim().toUpperCase(),
      firstName: patient.first_name || '',
      lastName: patient.last_name || '',
      dob: patient.date_of_birth || '',
      ageYears: years,
      ageMonths: months,
      ageInput: years > 0 ? `${years} years` : `${months} months`,
      ageMode: 'dob',
      sex: patient.gender || 'Male',
      mobile: patient.phone || '',
      email: patient.email || '',
      motherName: patient.mother_name || ''
    }));
    setPatientSearchTerm('');
  };

  // Handle form input changes with special logic
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    setFormData(prev => {
      const updated = { ...prev, [name]: newValue };

      // Auto-uppercase patient name
      if (name === 'patientName' || name === 'firstName' || name === 'lastName') {
        updated[name] = newValue.toUpperCase();
        if (name === 'firstName' || name === 'lastName') {
          updated.patientName = `${updated.firstName} ${updated.lastName}`.trim();
        }
      }

      // Auto-gender selection based on title
      if (name === 'title') {
        if (newValue === 'Mr.' || newValue === 'Master') {
          updated.sex = 'Male';
        } else if (newValue === 'Mrs.' || newValue === 'Ms.' || newValue === 'Miss') {
          updated.sex = 'Female';
        } else if (newValue === 'Baby' || newValue === 'B/Q (Baby/Queen)') {
          // For baby patients, keep current gender or default to Male
          if (!updated.sex) {
            updated.sex = 'Male';
          }
        }
        // For 'Dr.' and other titles, keep current gender selection
      }

      // Handle age mode switching
      if (name === 'ageMode') {
        if (newValue === 'manual') {
          updated.dob = '';
          updated.ageYears = '';
          updated.ageMonths = '';
          updated.ageInput = '';
        } else if (newValue === 'dob') {
          updated.ageYears = '';
          updated.ageMonths = '';
          updated.ageInput = '';
        }
      }

      // Auto-calculate age from DOB
      if (name === 'dob' && newValue) {
        const { years, months } = calculateAgeFromDOB(newValue);
        updated.ageYears = years;
        updated.ageMonths = months;
        updated.ageInput = years > 0 ? `${years} years` : `${months} months`;
      }

      // Auto-calculate DOB from manual age entry
      if ((name === 'ageYears' || name === 'ageMonths') && formData.ageMode === 'manual') {
        const years = name === 'ageYears' ? parseInt(newValue) || 0 : parseInt(updated.ageYears) || 0;
        const months = name === 'ageMonths' ? parseInt(newValue) || 0 : parseInt(updated.ageMonths) || 0;

        if (years > 0 || months > 0) {
          const today = new Date();
          const birthDate = new Date(today.getFullYear() - years, today.getMonth() - months, today.getDate());
          updated.dob = birthDate.toISOString().split('T')[0];
          updated.ageInput = years > 0 ? `${years} years` : `${months} months`;
        }
      }

      // Update age text when manually entered
      if (name === 'ageInput' && formData.ageMode === 'manual') {
        // Parse age input like "25 years" or "6 months"
        const ageText = newValue.toLowerCase();
        if (ageText.includes('year')) {
          const years = parseInt(ageText.match(/\d+/)?.[0]) || 0;
          updated.ageYears = years;
          updated.ageMonths = 0;
          if (years > 0) {
            const today = new Date();
            const birthDate = new Date(today.getFullYear() - years, today.getMonth(), today.getDate());
            updated.dob = birthDate.toISOString().split('T')[0];
          }
        } else if (ageText.includes('month')) {
          const months = parseInt(ageText.match(/\d+/)?.[0]) || 0;
          updated.ageYears = 0;
          updated.ageMonths = months;
          if (months > 0) {
            const today = new Date();
            const birthDate = new Date(today.getFullYear(), today.getMonth() - months, today.getDate());
            updated.dob = birthDate.toISOString().split('T')[0];
          }
        }
      }

      // Handle discount calculations
      if (name === 'discountPercent' && formData.discountType === 'percentage') {
        const billAmount = formData.billAmount + parseFloat(formData.otherCharges || 0);
        const discountAmount = (billAmount * parseFloat(newValue || 0)) / 100;
        updated.discountAmount = discountAmount;
        updated.totalAmount = billAmount - discountAmount;
        updated.balanceToBePaid = updated.totalAmount - parseFloat(formData.amountPaid || 0);
      }

      if (name === 'discountAmount' && formData.discountType === 'amount') {
        const billAmount = formData.billAmount + parseFloat(formData.otherCharges || 0);
        const discountAmount = parseFloat(newValue || 0);
        const discountPercent = billAmount > 0 ? (discountAmount / billAmount) * 100 : 0;
        updated.discountPercent = discountPercent;
        updated.totalAmount = billAmount - discountAmount;
        updated.balanceToBePaid = updated.totalAmount - parseFloat(formData.amountPaid || 0);
      }

      // Handle discount type switching
      if (name === 'discountType') {
        if (newValue === 'percentage') {
          // Keep percentage, recalculate amount
          const billAmount = formData.billAmount + parseFloat(formData.otherCharges || 0);
          const discountAmount = (billAmount * parseFloat(formData.discountPercent || 0)) / 100;
          updated.discountAmount = discountAmount;
        } else {
          // Keep amount, recalculate percentage
          const billAmount = formData.billAmount + parseFloat(formData.otherCharges || 0);
          const discountPercent = billAmount > 0 ? (parseFloat(formData.discountAmount || 0) / billAmount) * 100 : 0;
          updated.discountPercent = discountPercent;
        }
      }

      // Auto-calculate payment amount if not manually set
      if (name === 'amountPaid' && !updated.paymentAmount) {
        updated.paymentAmount = parseFloat(newValue) || 0;
        updated.balanceToBePaid = updated.totalAmount - parseFloat(newValue || 0);
      }

      return updated;
    });
  };

  // Handle test addition
  const handleAddTest = (test) => {
    const newTest = {
      id: test.id,
      name: test.name,
      price: test.price
    };
    
    setFormData(prev => ({
      ...prev,
      tests: [...prev.tests, newTest]
    }));
    
    calculateTotals();
  };

  // Handle test removal
  const handleRemoveTest = (testId) => {
    setFormData(prev => ({
      ...prev,
      tests: prev.tests.filter(test => test.id !== testId)
    }));
    
    calculateTotals();
  };

  // Enhanced calculation function
  const calculateTotals = useCallback(() => {
    const testsTotal = formData.tests.reduce((sum, test) => sum + test.price, 0);
    const billAmount = testsTotal + parseFloat(formData.otherCharges || 0);

    // Calculate discount
    let discountAmount = 0;
    if (formData.discountType === 'percentage') {
      discountAmount = (billAmount * parseFloat(formData.discountPercent || 0)) / 100;
    } else {
      discountAmount = parseFloat(formData.discountAmount || 0);
    }

    const totalAmount = billAmount - discountAmount;
    const balanceToBePaid = totalAmount - parseFloat(formData.amountPaid || 0);

    setFormData(prev => ({
      ...prev,
      billAmount,
      discountAmount: formData.discountType === 'percentage' ? discountAmount : prev.discountAmount,
      discountPercent: formData.discountType === 'amount' ? ((discountAmount / billAmount) * 100).toFixed(2) : prev.discountPercent,
      totalAmount,
      balanceToBePaid
    }));
  }, [formData.tests, formData.otherCharges, formData.discountType, formData.discountPercent, formData.discountAmount, formData.amountPaid]);

  // Recalculate when relevant fields change
  useEffect(() => {
    calculateTotals();
  }, [calculateTotals]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Enhanced validation
    if (formData.tests.length === 0) {
      setError('Please add at least one test');
      return;
    }

    if (!formData.patientName.trim() && (!formData.firstName.trim() || !formData.lastName.trim())) {
      setError('Patient name is required');
      return;
    }

    if (!formData.mobile.trim()) {
      setError('Mobile number is required');
      return;
    }

    // Validate discount remarks if discount is applied
    if ((formData.discountPercent > 0 || formData.discountAmount > 0) && !formData.discountRemarks.trim()) {
      setError('Discount remarks are required when applying discount');
      return;
    }

    // Validate mother's name for baby patients
    if (isBabyPatient() && !formData.motherName.trim()) {
      setError('Mother\'s name is required for baby patients');
      return;
    }

    setLoading(true);
    setError('');

    try {
      let patientId = selectedPatient?.id;

      // Create new patient if not selected from search
      if (!selectedPatient) {
        const patientData = {
          title: formData.title,
          first_name: formData.firstName || formData.patientName.split(' ')[0],
          last_name: formData.lastName || formData.patientName.split(' ').slice(1).join(' '),
          gender: formData.sex,
          date_of_birth: formData.dob,
          phone: formData.mobile,
          email: formData.email,
          address: formData.address || '',
          mother_name: formData.motherName,
          tenant_id: formData.branch || currentUser?.tenant_id
        };

        try {
          const patientResponse = await patientAPI.createPatient(patientData);
          patientId = patientResponse.data.id;
        } catch (patientError) {
          console.error('Error creating patient:', patientError);
          setError('Failed to create patient record. Please try again.');
          return;
        }
      }

      // Prepare billing data matching backend expectations
      const billingData = {
        // Required fields for backend validation
        patient_id: patientId,
        items: formData.tests.map(test => ({
          test_id: test.id,
          test_name: test.name,
          amount: test.price,
          quantity: 1
        })),
        total_amount: formData.totalAmount || 0,

        // Additional billing information
        bill_amount: formData.billAmount || 0,
        other_charges: formData.otherCharges || 0,
        other_charges_description: formData.otherChargesDescription || '',
        discount_percent: formData.discountPercent || 0,
        discount: formData.discountAmount || 0,
        subtotal: formData.billAmount || 0,
        paid_amount: formData.amountPaid || 0,
        payment_method: formData.paymentMethod || '',
        notes: formData.generalRemarks || '',
        branch: formData.branch || '',

        // Extended billing information for comprehensive tracking
        patient_name: formData.patientName || `${formData.firstName} ${formData.lastName}`.trim().toUpperCase(),
        registration_date: formData.date,
        sid_number: formData.sidNo,
        category: formData.category,
        discount_type: formData.discountType,
        discount_remarks: formData.discountRemarks,
        balance_amount: formData.balanceToBePaid,
        payment_date: formData.paymentDate,
        payment_amount: formData.paymentAmount,
        bank_name: formData.bankName,
        reference_number: formData.referenceNumber,
        sample_collect_datetime: formData.sampleCollectDateTime,
        clinical_remarks: formData.clinicalRemarks,
        emergency: formData.emergency,
        delivery_mode: formData.deliveryMode,
        final_report_date: formData.finalReportDate,
        referrer: formData.referrer,
        referral_source: formData.source,
        collection_boy: formData.collectionBoy,
        mother_name: formData.motherName,
        study_number: formData.studyNo,
        sub_period: formData.subPeriod,
        subject_period: formData.subjectPeriod,
        subject_number: formData.subNo,
        created_by: currentUser?.id,
        tenant_id: formData.branch || currentUser?.tenant_id
      };

      const response = await billingService.createBilling(billingData);

      if (response.success) {
        setSuccess('Billing registration completed successfully!');
        setTimeout(() => {
          navigate('/billing/reports');
        }, 2000);
      } else {
        setError(response.error || 'Failed to create billing record');
      }
    } catch (error) {
      console.error('Error submitting billing:', error);
      setError('Failed to submit billing. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Clear form with all new fields
  const handleClear = () => {
    setFormData({
      // Section 1: Branch & Registration Details
      branch: currentUser?.tenant_id || '',
      date: new Date().toISOString().split('T')[0],
      no: '',
      sidDate: new Date().toISOString().split('T')[0],
      sidNo: '',
      category: 'Normal',

      // Section 2: Patient Information
      patientCode: '',
      title: 'Mr.',
      patientName: '',
      firstName: '',
      lastName: '',
      dob: '',
      ageYears: '',
      ageMonths: '',
      ageInput: '',
      ageMode: 'dob',
      sex: 'Male',
      mobile: '',
      email: '',
      referrer: 'Doctor',
      source: '',
      collectionBoy: '',
      motherName: '',

      // Section 3: Test Selection
      tests: [],

      // Section 4: Billing Details
      sampleCollectDateTime: new Date().toISOString().slice(0, 16),
      billAmount: 0,
      otherCharges: 0,
      otherChargesDescription: '',
      discountType: 'percentage',
      discountPercent: 0,
      discountAmount: 0,
      discountRemarks: '',
      totalAmount: 0,
      amountPaid: 0,
      balanceToBePaid: 0,
      finalReportDate: '',

      // Section 5: Payment Details
      paymentMethod: 'Cash',
      bankName: '',
      referenceNumber: '',
      paymentDate: new Date().toISOString().split('T')[0],
      paymentAmount: 0,

      // Section 6: Clinical & Remarks
      clinicalRemarks: '',
      generalRemarks: '',
      emergency: false,
      deliveryMode: 'Lab Pickup',

      // Section 7: Study/Research Details
      studyNo: '',
      subPeriod: '',
      subjectPeriod: '',
      subNo: ''
    });
    setSelectedPatient(null);
    setPatientSearchTerm('');
    generateSID();
  };

  return (
    <div className="billing-registration-compact">
      {/* Header */}
      <div className="header-section">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h4 className="mb-1">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
              Billing Registration
            </h4>
            <p className="text-muted mb-0 small">Single form for new & existing patients</p>
          </div>
          <div className="d-flex gap-2">
            <Button variant="outline-secondary" onClick={() => navigate('/billing/reports')}>
              <FontAwesomeIcon icon={faArrowLeft} className="me-1" />
              Back
            </Button>
            <Button variant="outline-warning" onClick={handleClear}>
              Clear Form
            </Button>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" className="mb-3">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert variant="success" className="mb-3">
          <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
          {success}
        </Alert>
      )}

      {/* Patient Search */}
      <div className="search-section mb-3">
        <PatientSearch
          onPatientSelect={handlePatientSelect}
          searchTerm={patientSearchTerm}
          setSearchTerm={setPatientSearchTerm}
          searchResults={searchResults}
          searching={searching}
        />
        {selectedPatient && (
          <div className="selected-patient-info">
            <Badge bg="success">
              <FontAwesomeIcon icon={faUser} className="me-1" />
              Existing Patient: {selectedPatient.first_name} {selectedPatient.last_name}
            </Badge>
          </div>
        )}
      </div>

      {/* Main Form */}
      <Form onSubmit={handleSubmit}>
        {/* Section 1: Branch & Registration Details */}
        <div className="form-section mb-3">
          <h6 className="section-title">
            <FontAwesomeIcon icon={faIdCard} className="me-2" />
            Branch & Registration Details
          </h6>
          <Row className="g-2">
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-primary fw-bold">Branch *</Form.Label>
                <Form.Select
                  size="sm"
                  name="branch"
                  value={formData.branch}
                  onChange={handleInputChange}
                  required
                >
                  {branchOptions.map(branch => (
                    <option key={branch} value={branch}>{branch}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-purple fw-bold">Date *</Form.Label>
                <Form.Control
                  size="sm"
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-primary fw-bold">No.</Form.Label>
                <Form.Control
                  size="sm"
                  type="text"
                  name="no"
                  value={formData.no}
                  onChange={handleInputChange}
                  placeholder="Manual entry"
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-purple fw-bold">SID Date</Form.Label>
                <Form.Control
                  size="sm"
                  type="date"
                  name="sidDate"
                  value={formData.sidDate}
                  onChange={handleInputChange}
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-primary fw-bold">SID No. *</Form.Label>
                <Form.Control
                  size="sm"
                  type="text"
                  name="sidNo"
                  value={formData.sidNo}
                  onChange={handleInputChange}
                  readOnly
                />
              </Form.Group>
            </Col>
            <Col md={2}>
              <Form.Group>
                <Form.Label className="small text-secondary fw-bold">Category</Form.Label>
                <Form.Select
                  size="sm"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                >
                  {categoryOptions.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>
        </div>

        <Row className="g-3">
          {/* Left Column - Patient Information */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Patient Information
              </h6>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Patient Code</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="patientCode"
                      value={formData.patientCode}
                      onChange={handleInputChange}
                      placeholder="Auto-filled"
                    />
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Title</Form.Label>
                    <Form.Select
                      size="sm"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                    >
                      {titleOptions.map(title => (
                        <option key={title} value={title}>{title}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3}>
                  <Form.Group>
                    <Form.Label className="small text-primary fw-bold">Sex *</Form.Label>
                    <Form.Select
                      size="sm"
                      name="sex"
                      value={formData.sex}
                      onChange={handleInputChange}
                      required
                    >
                      {genderOptions.map(gender => (
                        <option key={gender} value={gender}>{gender}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mt-2">
                <Form.Label className="small text-primary fw-bold">Patient Name *</Form.Label>
                <Form.Control
                  size="sm"
                  type="text"
                  name="patientName"
                  value={formData.patientName}
                  onChange={handleInputChange}
                  placeholder="Enter full name (auto-uppercase)"
                  required
                />
              </Form.Group>

              {/* Age Input Section */}
              <div className="mt-2">
                <Form.Label className="small text-secondary fw-bold">Age Entry Mode</Form.Label>
                <div className="d-flex gap-3 mb-2">
                  <Form.Check
                    type="radio"
                    name="ageMode"
                    value="dob"
                    checked={formData.ageMode === 'dob'}
                    onChange={handleInputChange}
                    label="Date of Birth"
                    className="small"
                  />
                  <Form.Check
                    type="radio"
                    name="ageMode"
                    value="manual"
                    checked={formData.ageMode === 'manual'}
                    onChange={handleInputChange}
                    label="Manual Age"
                    className="small"
                  />
                </div>

                {formData.ageMode === 'dob' ? (
                  <Row className="g-2">
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label className="small text-purple fw-bold">Date of Birth</Form.Label>
                        <Form.Control
                          size="sm"
                          type="date"
                          name="dob"
                          value={formData.dob}
                          onChange={handleInputChange}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3}>
                      <Form.Group>
                        <Form.Label className="small text-secondary">Years</Form.Label>
                        <Form.Control
                          size="sm"
                          type="text"
                          value={formData.ageYears}
                          readOnly
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3}>
                      <Form.Group>
                        <Form.Label className="small text-secondary">Months</Form.Label>
                        <Form.Control
                          size="sm"
                          type="text"
                          value={formData.ageMonths}
                          readOnly
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                ) : (
                  <Row className="g-2">
                    <Col md={4}>
                      <Form.Group>
                        <Form.Label className="small text-secondary fw-bold">Years</Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          name="ageYears"
                          value={formData.ageYears}
                          onChange={handleInputChange}
                          placeholder="25"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group>
                        <Form.Label className="small text-secondary fw-bold">Months</Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          name="ageMonths"
                          value={formData.ageMonths}
                          onChange={handleInputChange}
                          placeholder="6"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group>
                        <Form.Label className="small text-secondary">Age Text</Form.Label>
                        <Form.Control
                          size="sm"
                          type="text"
                          name="ageInput"
                          value={formData.ageInput}
                          onChange={handleInputChange}
                          placeholder="25 years"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                )}
              </div>

              <Row className="g-2 mt-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-primary fw-bold">Mobile *</Form.Label>
                    <Form.Control
                      size="sm"
                      type="tel"
                      name="mobile"
                      value={formData.mobile}
                      onChange={handleInputChange}
                      placeholder="10-digit mobile"
                      required
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Email</Form.Label>
                    <Form.Control
                      size="sm"
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Referrer Type</Form.Label>
                    <Form.Select
                      size="sm"
                      name="referrer"
                      value={formData.referrer}
                      onChange={handleInputChange}
                    >
                      {referralSources.map(source => (
                        <option key={source} value={source}>{source}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Referral Source</Form.Label>
                    <Form.Select
                      size="sm"
                      name="source"
                      value={formData.source}
                      onChange={handleInputChange}
                    >
                      <option value="">Select {formData.referrer}</option>
                      {referralOptions[formData.referrer]?.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              <Form.Group className="mt-2">
                <Form.Label className="small text-secondary fw-bold">Collection Boy</Form.Label>
                <Form.Select
                  size="sm"
                  name="collectionBoy"
                  value={formData.collectionBoy}
                  onChange={handleInputChange}
                >
                  <option value="">Select Collection Boy</option>
                  {collectionBoys.map(boy => (
                    <option key={boy} value={boy}>{boy}</option>
                  ))}
                </Form.Select>
              </Form.Group>

              {/* Mother Name for Baby Patients */}
              {isBabyPatient() && (
                <Form.Group className="mt-2">
                  <Form.Label className="small text-primary fw-bold">Mother's Name *</Form.Label>
                  <Form.Control
                    size="sm"
                    type="text"
                    name="motherName"
                    value={formData.motherName}
                    onChange={handleInputChange}
                    placeholder="Mother's full name"
                    required={isBabyPatient()}
                  />
                </Form.Group>
              )}
            </div>
          </Col>

          {/* Center Column - Test Selection */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faFlask} className="me-2" />
                Test/Profile Selection
              </h6>

              {/* Test Selection */}
              <div className="test-selection mb-3">
                <Form.Label className="small text-primary fw-bold">Select Test/Profile *</Form.Label>
                <InputGroup size="sm">
                  <Form.Control
                    type="text"
                    placeholder="Search tests/profiles..."
                    value={testSearchTerm}
                    onChange={(e) => setTestSearchTerm(e.target.value)}
                  />
                  <InputGroup.Text>
                    <FontAwesomeIcon icon={faFlask} />
                  </InputGroup.Text>
                </InputGroup>

                {testSearchTerm && (
                  <div className="test-dropdown">
                    {availableTests
                      .filter(test => test.name.toLowerCase().includes(testSearchTerm.toLowerCase()))
                      .slice(0, 8)
                      .map(test => (
                        <div
                          key={test.id}
                          className="test-item"
                          onClick={() => {
                            handleAddTest(test);
                            setTestSearchTerm('');
                          }}
                        >
                          <div className="test-details">
                            <div className="test-code">T{test.id.toString().padStart(3, '0')}</div>
                            <div className="test-name">{test.name}</div>
                          </div>
                          <span className="test-price">₹{test.price}</span>
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>

              {/* Selected Tests Table */}
              {formData.tests.length > 0 && (
                <div className="selected-tests mb-3">
                  <Form.Label className="small text-success fw-bold">Selected Tests ({formData.tests.length})</Form.Label>
                  <div className="tests-table">
                    <div className="table-header">
                      <span>Code</span>
                      <span>Test Name</span>
                      <span>Price</span>
                      <span>Action</span>
                    </div>
                    {formData.tests.map(test => (
                      <div key={test.id} className="test-row">
                        <span className="test-code">T{test.id.toString().padStart(3, '0')}</span>
                        <span className="test-name">{test.name}</span>
                        <span className="test-price">₹{test.price}</span>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() => handleRemoveTest(test.id)}
                          className="remove-btn"
                        >
                          <FontAwesomeIcon icon={faTimes} />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {formData.tests.length === 0 && (
                <div className="no-tests-message">
                  <p className="text-muted small text-center py-3">
                    <FontAwesomeIcon icon={faFlask} className="me-2" />
                    No tests selected. Search and add tests above.
                  </p>
                </div>
              )}
            </div>
          </Col>

          {/* Right Column - Billing Details */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Billing Details
              </h6>

              <Form.Group className="mb-2">
                <Form.Label className="small text-purple fw-bold">Sample Collect Date Time</Form.Label>
                <Form.Control
                  size="sm"
                  type="datetime-local"
                  name="sampleCollectDateTime"
                  value={formData.sampleCollectDateTime}
                  onChange={handleInputChange}
                />
              </Form.Group>

              {/* Billing Calculations */}
              <div className="billing-calculations">
                <Row className="g-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Bill Amount</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.billAmount.toFixed(2)}
                        readOnly
                        className="fw-bold"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Other Charges</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        name="otherCharges"
                        value={formData.otherCharges}
                        onChange={handleInputChange}
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {/* Other Charges Description */}
                {formData.otherCharges > 0 && (
                  <Form.Group className="mt-2">
                    <Form.Label className="small text-success fw-bold">Other Charges Description</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="otherChargesDescription"
                      value={formData.otherChargesDescription}
                      onChange={handleInputChange}
                      placeholder="Describe the additional charges (e.g., Home collection, Express service, etc.)"
                    />
                  </Form.Group>
                )}

                <Row className="g-2 mt-2">
                </Row>

                {/* Discount Section */}
                <div className="discount-section mt-2">
                  <Form.Label className="small text-success fw-bold">Discount Options</Form.Label>
                  <div className="d-flex gap-3 mb-2">
                    <Form.Check
                      type="radio"
                      name="discountType"
                      value="percentage"
                      checked={formData.discountType === 'percentage'}
                      onChange={handleInputChange}
                      label="Percentage"
                      className="small"
                    />
                    <Form.Check
                      type="radio"
                      name="discountType"
                      value="amount"
                      checked={formData.discountType === 'amount'}
                      onChange={handleInputChange}
                      label="Fixed Amount"
                      className="small"
                    />
                  </div>

                  <Row className="g-2">
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label className="small text-success">
                          {formData.discountType === 'percentage' ? 'Discount %' : 'Discount Amount'}
                        </Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          name={formData.discountType === 'percentage' ? 'discountPercent' : 'discountAmount'}
                          value={formData.discountType === 'percentage' ? formData.discountPercent : formData.discountAmount}
                          onChange={handleInputChange}
                          placeholder="0"
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group>
                        <Form.Label className="small text-success">Discount Value</Form.Label>
                        <Form.Control
                          size="sm"
                          type="number"
                          value={formData.discountAmount.toFixed(2)}
                          readOnly
                          className="text-success fw-bold"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {(formData.discountPercent > 0 || formData.discountAmount > 0) && (
                    <Form.Group className="mt-2">
                      <Form.Label className="small text-success fw-bold">Discount Remarks *</Form.Label>
                      <Form.Select
                        size="sm"
                        name="discountRemarks"
                        value={formData.discountRemarks}
                        onChange={handleInputChange}
                        required={formData.discountPercent > 0 || formData.discountAmount > 0}
                      >
                        <option value="">Select discount reason</option>
                        {discountReasons.map(reason => (
                          <option key={reason} value={reason}>{reason}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  )}
                </div>

                <Row className="g-2 mt-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Total Amount</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.totalAmount.toFixed(2)}
                        readOnly
                        className="fw-bold text-primary"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Amount Paid</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        name="amountPaid"
                        value={formData.amountPaid}
                        onChange={handleInputChange}
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="g-2 mt-1">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Balance to be Paid</Form.Label>
                      <Form.Control
                        size="sm"
                        type="number"
                        value={formData.balanceToBePaid.toFixed(2)}
                        readOnly
                        className={formData.balanceToBePaid > 0 ? 'text-danger fw-bold' : 'text-success fw-bold'}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-purple fw-bold">Final Report Date</Form.Label>
                      <Form.Control
                        size="sm"
                        type="date"
                        name="finalReportDate"
                        value={formData.finalReportDate}
                        onChange={handleInputChange}
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </div>
            </div>
          </Col>
        </Row>

        {/* Bottom Sections */}
        <Row className="g-3 mt-3">
          {/* Section 5: Payment Details */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faCreditCard} className="me-2" />
                Payment Details
              </h6>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-success fw-bold">Payment Method</Form.Label>
                    <Form.Select
                      size="sm"
                      name="paymentMethod"
                      value={formData.paymentMethod}
                      onChange={handleInputChange}
                    >
                      {paymentMethods.map(method => (
                        <option key={method} value={method}>{method}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-purple fw-bold">Payment Date</Form.Label>
                    <Form.Control
                      size="sm"
                      type="date"
                      name="paymentDate"
                      value={formData.paymentDate}
                      onChange={handleInputChange}
                    />
                  </Form.Group>
                </Col>
              </Row>

              {/* Conditional Bank Details */}
              {(formData.paymentMethod === 'Bank Transfer' || formData.paymentMethod === 'Cheque') && (
                <Row className="g-2 mt-2">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Bank Name</Form.Label>
                      <Form.Select
                        size="sm"
                        name="bankName"
                        value={formData.bankName}
                        onChange={handleInputChange}
                      >
                        <option value="">Select Bank</option>
                        {bankOptions.map(bank => (
                          <option key={bank} value={bank}>{bank}</option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="small text-success fw-bold">Reference Number</Form.Label>
                      <Form.Control
                        size="sm"
                        type="text"
                        name="referenceNumber"
                        value={formData.referenceNumber}
                        onChange={handleInputChange}
                        placeholder="Transaction/Cheque No."
                      />
                    </Form.Group>
                  </Col>
                </Row>
              )}

              <Form.Group className="mt-2">
                <Form.Label className="small text-success fw-bold">Payment Amount</Form.Label>
                <Form.Control
                  size="sm"
                  type="number"
                  name="paymentAmount"
                  value={formData.paymentAmount}
                  onChange={handleInputChange}
                  placeholder="Actual payment amount"
                />
              </Form.Group>
            </div>
          </Col>

          {/* Section 6: Clinical & Remarks */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Clinical & Remarks
              </h6>

              <Form.Group className="mb-2">
                <Form.Label className="small text-orange fw-bold">Clinical Remarks</Form.Label>
                <Form.Control
                  size="sm"
                  as="textarea"
                  rows={2}
                  name="clinicalRemarks"
                  value={formData.clinicalRemarks}
                  onChange={handleInputChange}
                  placeholder="Medical notes and observations"
                />
              </Form.Group>

              <Form.Group className="mb-2">
                <Form.Label className="small text-secondary fw-bold">General Remarks</Form.Label>
                <Form.Control
                  size="sm"
                  as="textarea"
                  rows={2}
                  name="generalRemarks"
                  value={formData.generalRemarks}
                  onChange={handleInputChange}
                  placeholder="Additional notes"
                />
              </Form.Group>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Check
                      type="checkbox"
                      name="emergency"
                      checked={formData.emergency}
                      onChange={handleInputChange}
                      label="Emergency Case"
                      className="small text-orange fw-bold"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Delivery Mode</Form.Label>
                    <Form.Select
                      size="sm"
                      name="deliveryMode"
                      value={formData.deliveryMode}
                      onChange={handleInputChange}
                    >
                      {deliveryModes.map(mode => (
                        <option key={mode} value={mode}>{mode}</option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
            </div>
          </Col>

          {/* Section 7: Study/Research Details */}
          <Col lg={4}>
            <div className="form-section">
              <h6 className="section-title">
                <FontAwesomeIcon icon={faFlask} className="me-2" />
                Study/Research Details
              </h6>

              <Row className="g-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Study No</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="studyNo"
                      value={formData.studyNo}
                      onChange={handleInputChange}
                      placeholder="Research study ID"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Sub. Period</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="subPeriod"
                      value={formData.subPeriod}
                      onChange={handleInputChange}
                      placeholder="Sub-period"
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row className="g-2 mt-2">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Subject Period</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="subjectPeriod"
                      value={formData.subjectPeriod}
                      onChange={handleInputChange}
                      placeholder="Subject observation period"
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group>
                    <Form.Label className="small text-secondary fw-bold">Sub No.</Form.Label>
                    <Form.Control
                      size="sm"
                      type="text"
                      name="subNo"
                      value={formData.subNo}
                      onChange={handleInputChange}
                      placeholder="Subject number"
                    />
                  </Form.Group>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>

        {/* Submit Button */}
        <div className="submit-section mt-4">
          <div className="d-flex justify-content-end gap-2">
            <Button
              variant="success"
              type="submit"
              disabled={loading || formData.tests.length === 0}
              className="px-4"
            >
              <FontAwesomeIcon icon={loading ? faSpinner : faSave} spin={loading} className="me-2" />
              {loading ? 'Saving...' : 'Save Billing Registration'}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default BillingRegistration;
